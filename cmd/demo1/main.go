package main

import (
	"fmt"
	"time"

	"github.com/dromara/carbon/v2"
	"github.com/salsowelim/go-hijri"
)

func main() {

	var curYear = 2024
	for month := 1; month <= 12; month++ {
		monthDate := carbon.CreateFromDate(curYear, month, 1)
		daysInMonth := monthDate.DaysInMonth()
		fmt.Printf("month: %d, daysInMonth: %d\n", month, daysInMonth)
		for day := 1; day <= daysInMonth; day++ {
			cc := monthDate.SetDay(day)
			y, m, d, _ := hijri.ToUmmAlQura(cc.StdTime())
			fmt.Printf("%s AD = %04d-%02d-%02d H (standard)， Java day: %s\n", cc.ToDateString(), y, m, d, getPasaran(cc.StdTime()))
		}
	}
}

func getPasaran(date time.Time) string {
	// 基准日期：1968-12-03 是 <PERSON><PERSON><PERSON> (JDN = 2440213)
	baseDate := time.Date(1968, 12, 3, 0, 0, 0, 0, time.UTC)
	// baseJDN := 2440213 // 通过 Meeus 算法或工具计算
	basePasaran := 0 // Kliwon

	// 计算目标日期的 JDN（简化为天数差）
	daysDiff := int(date.Sub(baseDate).Hours() / 24)
	pasaranIndex := (basePasaran + daysDiff) % 5
	if pasaranIndex < 0 {
		pasaranIndex += 5
	}

	pasaranNames := []string{"Kliwon", "Legi", "Pahing", "Pon", "Wage"}
	weekdayNames := []string{"Minggu", "Senin", "Selasa", "Rebo", "Kemis", "Jumat", "Setu"}

	// 获取星期
	weekday := int(date.Weekday())
	return fmt.Sprintf("%s %s", weekdayNames[weekday], pasaranNames[pasaranIndex])
}
