// Package main implements a prayer times calculator that replicates the exact functionality
// of the "Jadwal Shalat" app using the Muslim World League calculation method.
//
// Based on reverse engineering analysis, this implementation correctly calculates:
// - Dhuha time using 4.5° interpretation (24 minutes after sunrise)
// - All other prayer times using Muslim World League method
// - Supports any location and timezone
package main

import (
	"fmt"
	"time"

	"github.com/hablullah/go-prayer"
)

// Coordinates represents geographical coordinates
type Coordinates struct {
	Latitude  float64
	Longitude float64
}

// PrayerTimes holds all prayer times for a day
type PrayerTimes struct {
	Fajr    time.Time
	Sunrise time.Time
	Dhuha   time.Time // Calculated using 4.5° interpretation (24 minutes after sunrise)
	Dhuhr   time.Time
	Asr     time.Time
	Maghrib time.Time
	Isha    time.Time
}

// GetIndonesianNames returns prayer times with Indonesian names as shown in the app
func (pt *PrayerTimes) GetIndonesianNames() map[string]time.Time {
	return map[string]time.Time{
		"Imsak":   pt.Fajr.Add(-10 * time.Minute), // Imsak is 10 minutes before Fajr
		"Subuh":   pt.Fajr,                        // Fajr prayer
		"Terbit":  pt.Sunrise,                     // Sunrise
		"Dhuha":   pt.Dhuha,                       // Dhuha prayer (4.5° interpretation)
		"Zuhur":   pt.Dhuhr,                       // Dhuhr prayer
		"Ashar":   pt.Asr,                         // Asr prayer
		"Maghrib": pt.Maghrib,                     // Maghrib prayer
		"Isya'":   pt.Isha,                        // Isha prayer
	}
}

// CalculateDhuhaTime calculates Dhuha time using 4.5° interpretation
// Based on reverse engineering analysis, 4.5° translates to 24 minutes after sunrise
func CalculateDhuhaTime(sunrise time.Time) time.Time {
	return sunrise.Add(24 * time.Minute)
}

// CalculateAccuratePrayerTimes calculates prayer times with correct Dhuha calculation
func CalculateAccuratePrayerTimes(date time.Time, coords Coordinates) (PrayerTimes, error) {
	// Use the third-party library for standard prayer times
	config := prayer.Config{
		Latitude:           coords.Latitude,
		Longitude:          coords.Longitude,
		Elevation:          0,
		Timezone:           date.Location(),
		TwilightConvention: prayer.MWL(),  // Muslim World League: Fajr -18°, Isha -17°
		AsrConvention:      prayer.Shafii, // Shafi method for Asr
		PreciseToSeconds:   true,          // Round to minutes
	}

	year := date.Year()
	schedules, err := prayer.Calculate(config, year)
	if err != nil {
		return PrayerTimes{}, fmt.Errorf("error calculating prayer times: %v", err)
	}

	dayOfYear := date.YearDay() - 1
	if dayOfYear >= len(schedules) {
		return PrayerTimes{}, fmt.Errorf("day of year out of range")
	}

	schedule := schedules[dayOfYear]

	// Calculate correct Dhuha time using 4.5° interpretation
	dhuhaTime := CalculateDhuhaTime(schedule.Sunrise)

	return PrayerTimes{
		Fajr:    schedule.Fajr,
		Sunrise: schedule.Sunrise,
		Dhuha:   dhuhaTime, // Correctly calculated using 4.5° interpretation
		Dhuhr:   schedule.Zuhr,
		Asr:     schedule.Asr,
		Maghrib: schedule.Maghrib,
		Isha:    schedule.Isha,
	}, nil
}

// DisplayJadwalShalat displays prayer times in the exact format as shown in the app
func DisplayJadwalShalat(date time.Time, coords Coordinates, locationName string) error {
	prayerTimes, err := CalculateAccuratePrayerTimes(date, coords)
	if err != nil {
		return err
	}

	indonesianTimes := prayerTimes.GetIndonesianNames()

	fmt.Printf("Jadwal Shalat - %s\n", date.Format("2006-01-02"))
	fmt.Printf("Metode: Muslim World League (Subuh -18° / Isya -17°)\n")
	fmt.Printf("Dhuha: 4.5° interpretation (24 minutes after sunrise)\n")
	fmt.Printf("Lokasi: %s (%.6f°, %.2f°)\n\n", locationName, coords.Latitude, coords.Longitude)

	prayers := []string{"Imsak", "Subuh", "Terbit", "Dhuha", "Zuhur", "Ashar", "Maghrib", "Isya'"}
	for _, prayer := range prayers {
		if t, exists := indonesianTimes[prayer]; exists {
			fmt.Printf("%-8s: %s\n", prayer, t.Format("15:04"))
		}
	}

	return nil
}

// GetPrayerTimesForDate returns prayer times for a specific date and location
func GetPrayerTimesForDate(date time.Time, latitude, longitude float64) (map[string]time.Time, error) {
	coords := Coordinates{
		Latitude:  latitude,
		Longitude: longitude,
	}

	prayerTimes, err := CalculateAccuratePrayerTimes(date, coords)
	if err != nil {
		return nil, err
	}

	return prayerTimes.GetIndonesianNames(), nil
}

// GetPrayerTimesForToday returns today's prayer times for a specific location
func GetPrayerTimesForToday(latitude, longitude float64, timezone string) (map[string]time.Time, error) {
	location, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("error loading timezone %s: %v", timezone, err)
	}

	today := time.Now().In(location)
	return GetPrayerTimesForDate(today, latitude, longitude)
}

// GetNextPrayer returns the next prayer time from the current time
func GetNextPrayer(currentTime time.Time, latitude, longitude float64) (string, time.Time, error) {
	prayerTimes, err := GetPrayerTimesForDate(currentTime, latitude, longitude)
	if err != nil {
		return "", time.Time{}, err
	}

	// Prayer order as they occur in a day
	prayerOrder := []string{"Subuh", "Terbit", "Dhuha", "Zuhur", "Ashar", "Maghrib", "Isya'"}

	for _, prayer := range prayerOrder {
		if prayerTime, exists := prayerTimes[prayer]; exists {
			if currentTime.Before(prayerTime) {
				return prayer, prayerTime, nil
			}
		}
	}

	// If no prayer found today, get tomorrow's Subuh
	tomorrow := currentTime.AddDate(0, 0, 1)
	tomorrowTimes, err := GetPrayerTimesForDate(tomorrow, latitude, longitude)
	if err != nil {
		return "", time.Time{}, err
	}

	return "Subuh", tomorrowTimes["Subuh"], nil
}

func main() {
	// Example usage with user's coordinates (Guangzhou/Shenzhen area)
	coords := Coordinates{
		Latitude:  23.118889,
		Longitude: 113.37,
	}

	// Set timezone to Shanghai
	shanghaiLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Printf("Error loading timezone: %v\n", err)
		return
	}

	today := time.Now().Add(time.Hour * 24 * 1).In(shanghaiLocation)

	fmt.Println("=== ACCURATE JADWAL SHALAT (Based on Reverse Engineering) ===")
	err = DisplayJadwalShalat(today, coords, "Guangzhou/Shenzhen area")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	// Show next prayer
	fmt.Println("\n--- Next Prayer ---")
	nextPrayer, nextTime, err := GetNextPrayer(today, coords.Latitude, coords.Longitude)
	if err != nil {
		fmt.Printf("Error getting next prayer: %v\n", err)
	} else {
		fmt.Printf("Next prayer: %s at %s\n", nextPrayer, nextTime.Format("15:04"))
		duration := nextTime.Sub(today)
		fmt.Printf("Time remaining: %v\n", duration.Round(time.Minute))
	}

	fmt.Println("\n--- API Usage Examples ---")

	// Example 1: Get today's prayer times using API
	fmt.Println("\n1. Today's prayer times (API):")
	todayTimes, err := GetPrayerTimesForToday(coords.Latitude, coords.Longitude, "Asia/Shanghai")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	prayers := []string{"Imsak", "Subuh", "Terbit", "Dhuha", "Zuhur", "Ashar", "Maghrib", "Isya'"}
	for _, prayer := range prayers {
		if t, exists := todayTimes[prayer]; exists {
			fmt.Printf("%-8s: %s\n", prayer, t.Format("15:04"))
		}
	}

}

