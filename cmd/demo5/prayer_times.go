package main

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// Prayer represents different prayer types
type Prayer int

const (
	Fajr Prayer = iota
	Sunrise
	Dhuha
	Dhuhr
	<PERSON>r
	<PERSON>
)

// <PERSON><PERSON> represents different Islamic schools of thought
type <PERSON><PERSON> int

const (
	<PERSON><PERSON><PERSON> = iota // Single shadow length
	Hanafi               // Double shadow length
)

// CalculationMethod represents different calculation methods
type CalculationMethod struct {
	Name         string
	FajrAngle    float64
	IshaAngle    float64
	IshaInterval int // minutes after <PERSON>gh<PERSON><PERSON> (0 if using angle)
	MaghribAngle float64
}

// Common calculation methods
var (
	MuslimWorldLeague     = CalculationMethod{"Muslim World League", 18.0, 17.0, 0, 0}
	Egyptian              = CalculationMethod{"Egyptian", 19.5, 17.7, 0, 0}
	Karachi               = CalculationMethod{"Karachi", 18.0, 18.0, 0, 0}
	UmmAlQura             = CalculationMethod{"Umm Al-Qura", 18.5, 0, 90, 0}
	Dubai                 = CalculationMethod{"Dubai", 18.2, 18.2, 0, 0}
	MoonsightingCommittee = CalculationMethod{"Moonsighting Committee", 18.0, 18.0, 0, 0}
	NorthAmerica          = CalculationMethod{"North America", 15.0, 15.0, 0, 0}
	Kuwait                = CalculationMethod{"Kuwait", 18.0, 17.5, 0, 0}
	Qatar                 = CalculationMethod{"Qatar", 18.0, 0, 90, 0}
	Singapore             = CalculationMethod{"Singapore", 20.0, 18.0, 0, 0}
)

// Coordinates represents geographical coordinates
type Coordinates struct {
	Latitude  float64
	Longitude float64
}

// PrayerTimes holds all prayer times for a day
type PrayerTimes struct {
	Fajr    time.Time
	Sunrise time.Time
	Dhuha   time.Time
	Dhuhr   time.Time
	Asr     time.Time
	Maghrib time.Time
	Isha    time.Time
}

// SolarTime handles astronomical calculations
type SolarTime struct {
	date        time.Time
	coordinates Coordinates
	transit     float64
	sunrise     float64
	sunset      float64
}

// NewSolarTime creates a new SolarTime instance
func NewSolarTime(date time.Time, coordinates Coordinates) *SolarTime {
	st := &SolarTime{
		date:        date,
		coordinates: coordinates,
	}
	st.calculate()
	return st
}

// calculate performs the main solar calculations
func (st *SolarTime) calculate() {
	// Julian day calculation
	jd := st.julianDay()

	// Solar calculations
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	// Declination
	declination := math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))

	// Equation of time
	eot := 4 * (L - 0.0057183 - math.Atan2(math.Tan(degToRad(lambda)), math.Cos(degToRad(23.439))))

	// Hour angle for sunrise/sunset (0 degrees)
	hourAngle := math.Acos(-math.Tan(degToRad(st.coordinates.Latitude)) * math.Tan(declination))

	// Transit, sunrise, sunset in decimal hours
	st.transit = 12 - st.coordinates.Longitude/15 - eot/60
	st.sunrise = st.transit - radToDeg(hourAngle)/15
	st.sunset = st.transit + radToDeg(hourAngle)/15
}

// julianDay calculates the Julian day number
func (st *SolarTime) julianDay() float64 {
	year := st.date.Year()
	month := int(st.date.Month())
	day := st.date.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	return float64(int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524)
}

// hourAngleForAngle calculates hour angle for a given solar angle
func (st *SolarTime) hourAngleForAngle(angle float64, afterTransit bool) float64 {
	// Simplified calculation - in real implementation, this would be more complex
	jd := st.julianDay()
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	declination := math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))

	// Hour angle calculation for the given angle
	cosHourAngle := (math.Sin(degToRad(angle)) - math.Sin(degToRad(st.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(st.coordinates.Latitude)) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		return math.NaN() // Sun doesn't reach this angle
	}

	hourAngle := math.Acos(cosHourAngle)

	if afterTransit {
		return st.transit + radToDeg(hourAngle)/15
	}
	return st.transit - radToDeg(hourAngle)/15
}

// asrTime calculates Asr prayer time based on shadow length
func (st *SolarTime) asrTime(madhab Madhab) float64 {
	// Shadow length factor: 1 for Shafi, 2 for Hanafi
	shadowFactor := 1.0
	if madhab == Hanafi {
		shadowFactor = 2.0
	}

	// Simplified Asr calculation
	// In reality, this involves more complex shadow length calculations
	jd := st.julianDay()
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	declination := math.Asin(math.Sin(degToRad(23.439)) * math.Sin(degToRad(lambda)))

	// Calculate solar altitude for Asr
	asrAltitude := math.Atan(1.0 / (shadowFactor + math.Tan(math.Abs(degToRad(st.coordinates.Latitude)-declination))))
	asrAngle := 90 - radToDeg(asrAltitude)

	return st.hourAngleForAngle(-asrAngle, true)
}

// CalculatePrayerTimes calculates all prayer times for a given date and location
func CalculatePrayerTimes(date time.Time, coordinates Coordinates, method CalculationMethod, madhab Madhab) *PrayerTimes {
	solarTime := NewSolarTime(date, coordinates)

	// Helper function to convert decimal hours to time.Time
	hoursToTime := func(hours float64) time.Time {
		if math.IsNaN(hours) {
			return time.Time{}
		}

		// Ensure hours is in valid range
		hours = math.Mod(hours+24, 24)

		h := int(hours)
		m := int((hours - float64(h)) * 60)
		s := int(((hours-float64(h))*60 - float64(m)) * 60)

		return time.Date(date.Year(), date.Month(), date.Day(), h, m, s, 0, date.Location())
	}

	// Calculate each prayer time
	fajrTime := solarTime.hourAngleForAngle(-method.FajrAngle, false)
	sunriseTime := solarTime.sunrise
	dhuhaTime := solarTime.hourAngleForAngle(4.5, false) // Dhuha is 4.5 degrees after sunrise
	dhuhrTime := solarTime.transit
	asrTime := solarTime.asrTime(madhab)
	maghribTime := solarTime.sunset

	var ishaTime float64
	if method.IshaInterval > 0 {
		// Use time interval after Maghrib
		ishaTime = maghribTime + float64(method.IshaInterval)/60.0
	} else {
		// Use angle
		ishaTime = solarTime.hourAngleForAngle(-method.IshaAngle, true)
	}

	return &PrayerTimes{
		Fajr:    hoursToTime(fajrTime),
		Sunrise: hoursToTime(sunriseTime),
		Dhuha:   hoursToTime(dhuhaTime),
		Dhuhr:   hoursToTime(dhuhrTime),
		Asr:     hoursToTime(asrTime),
		Maghrib: hoursToTime(maghribTime),
		Isha:    hoursToTime(ishaTime),
	}
}

// Helper functions
func degToRad(degrees float64) float64 {
	return degrees * math.Pi / 180.0
}

func radToDeg(radians float64) float64 {
	return radians * 180.0 / math.Pi
}

// FormatTime formats time in 24-hour format
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return "N/A"
	}
	return t.Format("15:04")
}

func main() {
	// Example usage
	// Coordinates for Mecca, Saudi Arabia
	mecca := Coordinates{Latitude: 21.4225, Longitude: 39.8262}

	// Current date
	date := time.Now()

	// Calculate prayer times using Muslim World League method with Shafi madhab
	prayerTimes := CalculatePrayerTimes(date, mecca, MuslimWorldLeague, Shafi)

	fmt.Printf("Prayer Times for %s (Mecca)\n", date.Format("2006-01-02"))
	fmt.Printf("Method: %s, Madhab: Shafi\n\n", MuslimWorldLeague.Name)

	fmt.Printf("Fajr:    %s\n", FormatTime(prayerTimes.Fajr))
	fmt.Printf("Sunrise: %s\n", FormatTime(prayerTimes.Sunrise))
	fmt.Printf("Dhuha:   %s\n", FormatTime(prayerTimes.Dhuha))
	fmt.Printf("Dhuhr:   %s\n", FormatTime(prayerTimes.Dhuhr))
	fmt.Printf("Asr:     %s\n", FormatTime(prayerTimes.Asr))
	fmt.Printf("Maghrib: %s\n", FormatTime(prayerTimes.Maghrib))
	fmt.Printf("Isha:    %s\n", FormatTime(prayerTimes.Isha))

	fmt.Println("\n" + strings.Repeat("=", 50))

	// Test different locations and methods
	locations := map[string]Coordinates{
		// "Istanbul":  {41.0082, 28.9784},
		// "Cairo":     {30.0444, 31.2357},
		// "Jakarta":   {-6.2088, 106.8456},
		// "London":    {51.5074, -0.1278},
		"Guangzhou": {23.116672, 113.375473},
	}

	// methods := []CalculationMethod{Egyptian, Karachi, UmmAlQura}
	methods := []CalculationMethod{MuslimWorldLeague}

	for city, coords := range locations {
		fmt.Printf("\n%s (%+.4f, %+.4f):\n", city, coords.Latitude, coords.Longitude)

		for _, method := range methods {
			times := CalculatePrayerTimes(date, coords, method, Shafi)
			fmt.Printf("  %s: Fajr %s, Dhuhr %s, Asr %s, Maghrib %s, Isha %s\n",
				method.Name,
				FormatTime(times.Fajr),
				FormatTime(times.Dhuhr),
				FormatTime(times.Asr),
				FormatTime(times.Maghrib),
				FormatTime(times.Isha))
		}
	}
}
