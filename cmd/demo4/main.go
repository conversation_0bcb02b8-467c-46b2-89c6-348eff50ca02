package main

import (
	"fmt"
	"time"

	"github.com/buildscientist/prayertime"
)

func main() {
	// 23.116672,113.375473
	location, _ := time.LoadLocation("Asia/Shanghai")
	_, offset := time.Now().In(location).Zone()
	timezone := float64(offset / 3600)
	fmt.Println("timezone", timezone)

	chicago := prayertime.New(23.116672, 113.375473, timezone)
	chicagoPrayerTime := prayertime.CalculatePrayerTimes(&chicago, time.Now())
	printPrayerTimes("GuangZhou", chicagoPrayerTime)
}

func printPrayerTimes(city string, prayertimes []string) {
	today := time.Now()
	fmt.Println()
	fmt.Println("=======" + city + "=======")
	fmt.Println(today.Month(), today.Day(), today.Year())

	for x := 0; x < len(prayertimes); x++ {
		fmt.Println(prayertime.PrayerTimeNames[x] + " - " + prayertimes[x])
	}

}
