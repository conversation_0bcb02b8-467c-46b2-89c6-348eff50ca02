package main

import (
	"fmt"
	"time"

	"github.com/hablullah/go-prayer"
)

func main() {
	// --- 配置 ---
	latitude := 23.1189
	longitude := 113.3700

	timezone := "Asia/Shanghai"

	loc, err := time.LoadLocation(timezone)
	if err != nil {
		panic(fmt.Sprintf("无法加载时区: %v", err))
	}
	date := time.Now().In(loc)

	// --- 核心逻辑：根据逆向工程分析设置自定义计算参数 ---
	customConvention := &prayer.TwilightConvention{
		FajrAngle: 20.0,
		IshaAngle: 18.0,
	}

	// 配置祈祷时间计算器 (不包含任何调整)
	cfg := prayer.Config{
		Latitude:           latitude,
		Longitude:          longitude,
		Timezone:           loc,
		TwilightConvention: customConvention,
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   true,
	}

	// --- 计算 ---
	schedules, err := prayer.Calculate(cfg, date.Year())
	if err != nil {
		panic(fmt.Sprintf("计算祈祷时间时出错: %v", err))
	}

	todaySchedule := schedules[date.YearDay()-1]

	// --- 手动应用 Ihtiyat (预防性) 调整和计算 Dhuha ---

	// Imsak: Fajr - 10 分钟 (标准)
	imsakTime := todaySchedule.Fajr.Add(-10 * time.Minute)

	// Dhuha: 根据用户反馈，是日出后的固定偏移量
	dhuhaTime := todaySchedule.Sunrise.Add(24 * time.Minute)

	// 手动应用用户反馈的调整
	zuhurTime := todaySchedule.Zuhr.Add(2 * time.Minute)
	asrTime := todaySchedule.Asr.Add(1 * time.Minute)
	maghribTime := todaySchedule.Maghrib.Add(1 * time.Minute)
	ishaTime := todaySchedule.Isha.Add(1 * time.Minute)

	// --- 输出结果 ---
	fmt.Println("Jadwal Shalat (Metode: Lembaga Falakiyah NU + Koreksi Manual)")
	fmt.Printf("Lokasi: Lat %.4f, Lon %.4f (%s)\n", latitude, longitude, timezone)
	fmt.Printf("Tanggal: %s\n\n", date.Format("2006-01-02"))

	fmt.Printf("Imsak   : %s\n", imsakTime.Format("15:04"))
	fmt.Printf("Subuh   : %s\n", todaySchedule.Fajr.Format("15:04"))
	fmt.Printf("Terbit  : %s\n", todaySchedule.Sunrise.Format("15:04"))
	fmt.Printf("Dhuha   : %s\n", dhuhaTime.Format("15:04"))
	fmt.Printf("Zuhur   : %s\n", zuhurTime.Format("15:04"))
	fmt.Printf("Ashar   : %s\n", asrTime.Format("15:04"))
	fmt.Printf("Maghrib : %s\n", maghribTime.Format("15:04"))
	fmt.Printf("Isya'   : %s\n", ishaTime.Format("15:04"))
}
