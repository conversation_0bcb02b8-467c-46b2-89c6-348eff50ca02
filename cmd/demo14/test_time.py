import datetime
from pytz import timezone
from astral import LocationInfo
from astral.sun import sun, time_at_elevation, SunDirection

def calculate_sun_and_dhuha_times(latitude: float, longitude: float, tz_string: str):
    """
    根据经纬度和时区，计算日出和Dhuha祷告的时间。

    Dhuha开始时间优先使用太阳仰角4.5度的方法计算，
    如果计算失败（例如在极地地区），则回退到“日出后20分钟”的方法。

    Args:
        latitude (float): 纬度 (北纬为正, 南纬为负)
        longitude (float): 经度 (东经为正, 西经为负)
        tz_string (str): IANA时区数据库中的时区名称 (例如 'Asia/Shanghai', 'Asia/Jakarta')

    Returns:
        dict: 包含日出和Dhuha时间信息的字典，如果无法获取时区则返回None。
    """
    try:
        # 验证并获取时区对象
        tz = timezone(tz_string)
    except Exception as e:
        print(f"错误: 无效的时区字符串 '{tz_string}'. 错误信息: {e}")
        return None

    # 1. 创建地点信息对象
    city = LocationInfo("Custom Location", "Region", tz_string, latitude, longitude)
    
    # 2. 获取今天的日期
    today = datetime.datetime.now(tz).date()

    # 3. 计算标准的太阳事件（日出、正午等）
    s = sun(city.observer, date=today)
    sunrise_time = s['sunrise']
    noon_time = s['noon'] # Zuhur 时间的基础

    # 4. 计算 Dhuha 开始时间
    dhuha_start_time = None
    try:
        # 优先使用天文学方法：计算太阳在地平线以上4.5度的时间
        dhuha_start_time = time_at_elevation(
            city.observer, 
            elevation=4.5, 
            date=today, 
            direction=SunDirection.RISING
        )
    except ValueError:
        # 如果上述方法失败（例如在极昼/极夜期间），则使用备用方法
        print("警告: 无法通过太阳仰角计算Dhuha开始时间，将使用 '日出+20分钟' 作为备用方案。")
        dhuha_start_time = sunrise_time + datetime.timedelta(minutes=20)

    # 5. 计算 Dhuha 结束时间（晌礼Zuhur前15分钟）
    dhuha_end_time = noon_time - datetime.timedelta(minutes=15)

    # 6. 计算 Dhuha 最佳时间（大约在日出和正午的中间点）
    morning_duration = noon_time - sunrise_time
    dhuha_best_time = sunrise_time + morning_duration / 2
    
    return {
        "location": {
            "latitude": latitude,
            "longitude": longitude,
            "timezone": tz_string
        },
        "date": today,
        "sunrise": sunrise_time,
        "dhuha_start": dhuha_start_time,
        "dhuha_best_time": dhuha_best_time,
        "dhuha_end_time": dhuha_end_time
    }

def format_time(dt_object):
    """辅助函数，用于格式化时间输出"""
    return dt_object.strftime('%Y-%m-%d %H:%M:%S %Z%z')

# --- 主程序入口 ---
if __name__ == "__main__":
    # ----- 在这里输入您的参数 -----
    # 示例1: 雅加达 (印度尼西亚)
    lat = -6.2088
    lon = 106.8456
    tz = "Asia/Jakarta"
    
    # 示例2: 广州 (中国)
    # lat = 23.1291
    # lon = 113.2644
    # tz = "Asia/Shanghai"

    # 执行计算
    times = calculate_sun_and_dhuha_times(lat, lon, tz)

    # 打印结果
    if times:
        print("="*40)
        print(f"地点: 纬度={times['location']['latitude']}, 经度={times['location']['longitude']}")
        print(f"时区: {times['location']['timezone']}")
        print(f"日期: {times['date'].strftime('%Y-%m-%d')}")
        print("="*40)
        print(f"日出时间 (Sunrise/Terbit):    {format_time(times['sunrise'])}")
        print(f"Dhuha 开始时间 (Start):        {format_time(times['dhuha_start'])}")
        print(f"Dhuha 最佳时间 (Best):         {format_time(times['dhuha_best_time'])}")
        print(f"Dhuha 结束时间 (End):          {format_time(times['dhuha_end_time'])}")
        print("="*40)
        print("\n说明:")
        print(" - Dhuha 开始时间基于太阳在地平线以上4.5度计算。")
        print(" - Dhuha 结束时间为当天正午前15分钟。")