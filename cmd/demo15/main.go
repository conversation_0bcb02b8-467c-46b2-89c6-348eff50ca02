// Correct 4.5° Dhuha calculation for integration into your existing code
package main

import (
	"fmt"
	"math"
	"time"

	"github.com/hablullah/go-prayer"
)

const (
	Deg2Rad = math.Pi / 180.0
	Rad2Deg = 180.0 / math.Pi
)

// Calculate Julian day number
func getJulianDayNumber(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	jd := math.Floor(365.25*float64(year+4716)) +
		math.Floor(30.6001*float64(month+1)) +
		float64(day) + float64(b) - 1524.5

	return jd
}

// Calculate solar declination
func getSolarDeclinationAngle(jd float64) float64 {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*Deg2Rad) + 0.020*math.Sin(2*g*Deg2Rad)

	return math.Asin(math.Sin(23.439*Deg2Rad)*math.Sin(lambda*Deg2Rad)) * Rad2Deg
}

// Calculate hour angle for given solar elevation
func getHourAngleForElevation(latitude, declination, elevation float64) float64 {
	latRad := latitude * Deg2Rad
	decRad := declination * Deg2Rad
	elevRad := elevation * Deg2Rad

	numerator := math.Sin(elevRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)

	if math.Abs(numerator/denominator) > 1 {
		return math.NaN() // No solution
	}

	return math.Acos(numerator/denominator) * Rad2Deg
}

// THIS IS THE FUNCTION TO REPLACE IN YOUR CODE
// Calculate Dhuha time using true 4.5° solar elevation angle
func calculateDhuhaTime(date time.Time, latitude, longitude float64, sunrise time.Time) time.Time {
	// Get astronomical parameters
	jd := getJulianDayNumber(date)
	declination := getSolarDeclinationAngle(jd)

	// Calculate hour angles for sunrise (-0.833°) and Dhuha (4.5°)
	sunriseHourAngle := getHourAngleForElevation(latitude, declination, -0.833)
	dhuhaHourAngle := getHourAngleForElevation(latitude, declination, 4.5)

	if math.IsNaN(sunriseHourAngle) || math.IsNaN(dhuhaHourAngle) {
		// Fallback to approximate 24 minutes if calculation fails
		return sunrise.Add(24 * time.Minute)
	}

	// Calculate time difference in hours
	// Both are on the morning side, so the difference is the offset
	timeDiffHours := (sunriseHourAngle - dhuhaHourAngle) / 15.0

	// Add this offset to sunrise time
	return sunrise.Add(time.Duration(timeDiffHours*3600) * time.Second)
}

// Test function to verify the calculation
func testDhuhaCalculation() {
	// User's coordinates
	latitude := 23.118889
	longitude := 113.37

	// Set timezone
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")

	// Test dates
	testDates := []string{"2025-08-19", "2025-08-20", "2025-08-21", "2025-08-22", "2025-08-23", "2025-08-24", "2025-08-25", "2025-08-26", "2025-08-27", "2025-08-28", "2025-08-29", "2025-08-30", "2025-08-31", "2025-09-01"}

	fmt.Println("🌅 Testing True 4.5° Dhuha Calculation")
	fmt.Println("=====================================")

	for _, dateStr := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)

		// Get sunrise using your preferred method (Diyanet)
		config := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: prayer.MWL(), // Your method
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}

		schedules, _ := prayer.Calculate(config, date.Year())
		dayOfYear := date.YearDay() - 1
		schedule := schedules[dayOfYear]

		// Calculate Dhuha using the true 4.5° method
		dhuha := calculateDhuhaTime(date, latitude, longitude, schedule.Sunrise)

		// Calculate offset from sunrise
		offset := dhuha.Sub(schedule.Sunrise)

		fmt.Printf("Date: %s\n", dateStr)
		fmt.Printf("  Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
		fmt.Printf("  Dhuha (4.5°): %s\n", dhuha.Format("15:04:05"))
		fmt.Printf("  Offset: %.1f minutes\n", offset.Minutes())
		fmt.Printf("  Expected: 06:28\n")

		// if dhuha.Format("15:04") == "06:28" {
		// 	fmt.Printf("  ✅ MATCHES!\n")
		// } else {
		// 	fmt.Printf("  ❌ Difference: %v\n", dhuha.Sub(time.Date(date.Year(), date.Month(), date.Day(), 6, 28, 0, 0, shanghaiLocation)))
		// }
		// fmt.Println()
	}
}

// Example of how to integrate into your existing code
func exampleIntegration() {
	fmt.Println("📝 Integration Example:")
	fmt.Println("Replace your existing calculateDhuhaTime function with:")
	fmt.Println()
	fmt.Println("```go")
	fmt.Println("func calculateDhuhaTime(date time.Time, latitude, longitude float64, sunrise time.Time) time.Time {")
	fmt.Println("    // Get astronomical parameters")
	fmt.Println("    jd := getJulianDayNumber(date)")
	fmt.Println("    declination := getSolarDeclinationAngle(jd)")
	fmt.Println("    ")
	fmt.Println("    // Calculate hour angles for sunrise (-0.833°) and Dhuha (4.5°)")
	fmt.Println("    sunriseHourAngle := getHourAngleForElevation(latitude, declination, -0.833)")
	fmt.Println("    dhuhaHourAngle := getHourAngleForElevation(latitude, declination, 4.5)")
	fmt.Println("    ")
	fmt.Println("    if math.IsNaN(sunriseHourAngle) || math.IsNaN(dhuhaHourAngle) {")
	fmt.Println("        return sunrise.Add(24 * time.Minute) // Fallback")
	fmt.Println("    }")
	fmt.Println("    ")
	fmt.Println("    // Calculate time difference and add to sunrise")
	fmt.Println("    timeDiffHours := (sunriseHourAngle - dhuhaHourAngle) / 15.0")
	fmt.Println("    return sunrise.Add(time.Duration(timeDiffHours * 3600) * time.Second)")
	fmt.Println("}")
	fmt.Println("```")
	fmt.Println()
	fmt.Println("Also add these helper functions:")
	fmt.Println("- getJulianDayNumber()")
	fmt.Println("- getSolarDeclinationAngle()")
	fmt.Println("- getHourAngleForElevation()")
}

func main() {
	testDhuhaCalculation()
	fmt.Println()
	// exampleIntegration()
}
