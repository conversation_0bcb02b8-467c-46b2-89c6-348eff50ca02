# Go Demo Repository

这是一个支持多个main函数的Go演示仓库，方便进行各种Go语言的实验和学习。

## 项目结构

```
demo/
├── cmd/                    # 各个demo的main函数
│   ├── demo1/
│   │   └── main.go        # Demo 1: Hello World
│   ├── demo2/
│   │   └── main.go        # Demo 2: 时间和数学计算
│   └── demo3/
│       └── main.go        # Demo 3: 环境变量和命令行参数
├── pkg/                    # 共享的包
│   └── utils/
│       └── utils.go       # 工具函数
├── go.mod                 # Go模块文件
├── Makefile              # 构建和运行脚本
└── README.md             # 项目说明
```

## 快速开始

### 运行单个demo

```bash
# 运行Demo 1
make demo1

# 运行Demo 2
make demo2

# 运行Demo 3
make demo3

# 或者直接使用go run
go run cmd/demo1/main.go
go run cmd/demo2/main.go
go run cmd/demo3/main.go arg1 arg2  # demo3支持命令行参数
```

### 运行所有demo

```bash
make run-all
```

### 构建二进制文件

```bash
# 构建所有demo
make build-all

# 构建单个demo
make build-demo1
make build-demo2
make build-demo3
```

### 其他命令

```bash
# 查看所有可用命令
make help

# 清理构建文件
make clean
```

## 添加新的Demo

1. 在`cmd/`目录下创建新的子目录，如`cmd/demo4/`
2. 在新目录中创建`main.go`文件
3. 在`Makefile`中添加相应的构建和运行规则
4. 可以使用`pkg/utils`包中的共享函数

## Demo说明

- **Demo 1**: 基础的Hello World程序，演示包的导入和使用
- **Demo 2**: 时间处理和简单的数学计算
- **Demo 3**: 环境变量读取和命令行参数处理

每个demo都是独立的，可以单独运行和构建。
