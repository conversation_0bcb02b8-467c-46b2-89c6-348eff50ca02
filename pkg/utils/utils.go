package utils

import (
	"fmt"
	"strings"
)

// FormatMessage 格式化消息，添加装饰性边框
func FormatMessage(title, content string) string {
	border := strings.Repeat("=", len(title)+4)
	return fmt.Sprintf("\n%s\n| %s |\n%s\n%s", border, title, border, content)
}

// Calculate 简单的加法计算
func Calculate(a, b int) int {
	return a + b
}

// Greet 生成问候语
func Greet(name string) string {
	if name == "" {
		name = "World"
	}
	return fmt.Sprintf("Hello, %s!", name)
}
