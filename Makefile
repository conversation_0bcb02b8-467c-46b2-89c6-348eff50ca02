.PHONY: demo1 demo2 demo3 build-all clean help

# 运行各个demo
demo1:
	@echo "Running Demo 1..."
	go run cmd/demo1/main.go

demo2:
	@echo "Running Demo 2..."
	go run cmd/demo2/main.go

demo3:
	@echo "Running Demo 3..."
	go run cmd/demo3/main.go

# 构建所有demo的二进制文件
build-all:
	@echo "Building all demos..."
	go build -o bin/demo1 cmd/demo1/main.go
	go build -o bin/demo2 cmd/demo2/main.go
	go build -o bin/demo3 cmd/demo3/main.go
	@echo "Binaries created in bin/ directory"

# 创建bin目录
bin:
	mkdir -p bin

# 构建单个demo
build-demo1: bin
	go build -o bin/demo1 cmd/demo1/main.go

build-demo2: bin
	go build -o bin/demo2 cmd/demo2/main.go

build-demo3: bin
	go build -o bin/demo3 cmd/demo3/main.go

# 运行所有demo
run-all: demo1 demo2 demo3

# 清理构建文件
clean:
	rm -rf bin/

# 显示帮助信息
help:
	@echo "Available commands:"
	@echo "  demo1      - Run demo 1"
	@echo "  demo2      - Run demo 2"
	@echo "  demo3      - Run demo 3"
	@echo "  run-all    - Run all demos"
	@echo "  build-all  - Build all demos to bin/ directory"
	@echo "  clean      - Remove built binaries"
	@echo "  help       - Show this help message"
